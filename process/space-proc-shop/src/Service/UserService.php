<?php

namespace App\Service;

use App\Trait\LoggerTrait;
use Space\MongoDocuments\Document\UserData;
use Space\MongoDocuments\Service\MongoDBService;
use Webmozart\Assert\Assert;

class UserService
{
    use LoggerTrait;

    public const COLLECTION = 'userData';

    public function __construct(
        private MongoDBService $mongoDBService
    ) {}

    /**
     * Retrieves user data from MongoDB based on user ID, VIN, and session ID.
     *
     * @param string|null $userId    The ID of the user.
     * @param string|null $vin       The Vehicle Identification Number.
     * @param string|null $sessionId The remote session ID.
     *
     * @return array A response containing the user data that matches the specified filters.
     */
    public function getUsersBySessionId(?string $userId, ?string $vin, ?string $sessionId): array
    {
        $this->logger->info('Getting users', ['sessionId' => $sessionId]);

        try {
            $repository = $this->mongoDBService->getDocumentManager()->getRepository(UserData::class);
            $queryBuilder = $repository->createQueryBuilder();

            if ($userId) {
                $queryBuilder->field('userId')->equals($userId);
            }

            if ($vin) {
                $queryBuilder->field('vehicle.vin')->equals($vin);
            }

            $users = $queryBuilder->getQuery()->execute()->toArray();

            // Filter by session ID if provided
            $result = [];
            foreach ($users as $user) {
                $userData = [
                    'id' => $user->getId(),
                    'userId' => $user->getUserId(),
                    'vehicles' => []
                ];

                foreach ($user->getVehicles() as $vehicle) {
                    if (!$vin || $vehicle->getVin() === $vin) {
                        $userData['vehicles'][] = [
                            'vin' => $vehicle->getVin(),
                            'brand' => $vehicle->getBrand(),
                            'model' => $vehicle->getModel(),
                            'version' => $vehicle->getVersion(),
                        ];
                    }
                }

                $result[] = $userData;
            }

            return $result;
        } catch (\Exception $e) {
            $this->logger->error('Error getting users by session ID', [
                'userId' => $userId,
                'vin' => $vin,
                'sessionId' => $sessionId,
                'error' => $e->getMessage()
            ]);
            return [];
        }
    }

    /**
     * Retrieves user data from MongoDB based on the user ID.
     *
     * @param string|null $userId The ID of the user to search for.
     *
     * @return array A response containing the user data that matches the specified user ID.
     */
    public function getUserByUserId(?string $userId): array
    {
        $this->logger->info('Getting user', ['userId' => $userId]);

        try {
            if (!$userId) {
                $this->logger->warning('No userId provided');
                return [];
            }

            // Log MongoDB service details
            $this->logger->info('MongoDB Service details', [
                'service_class' => get_class($this->mongoDBService),
                'database' => $_ENV['MONGODB_DB'] ?? 'unknown'
            ]);

            // Log the query being executed
            $this->logger->info('Executing MongoDB findOneBy query', [
                'document_class' => UserData::class,
                'query' => ['userId' => $userId]
            ]);

            $user = $this->mongoDBService->findOneBy(UserData::class, ['userId' => $userId]);

            if (!$user) {
                $this->logger->warning('User not found in MongoDB', [
                    'userId' => $userId,
                    'document_class' => UserData::class
                ]);

                // Try to get some sample data to verify connection
                try {
                    $allUsers = $this->mongoDBService->findBy(UserData::class, [], null, 5); // Get first 5 users
                    $this->logger->info('Sample users in collection', [
                        'sample_count' => count($allUsers),
                        'sample_userIds' => array_map(function($u) { return $u->getUserId(); }, $allUsers)
                    ]);
                } catch (\Exception $e) {
                    $this->logger->error('Error getting sample users', ['error' => $e->getMessage()]);
                }

                return [];
            }

            $this->logger->info('User found successfully', [
                'userId' => $userId,
                'userObjectId' => $user->getId(),
                'vehicleCount' => count($user->getVehicles()),
                'userPsaIdCount' => $user->getUserPsaId() ? count($user->getUserPsaId()) : 0
            ]);

            $vehicles = [];
            $vehicleIndex = 0;
            foreach ($user->getVehicles() as $vehicle) {
                $this->logger->info('Processing vehicle', [
                    'vehicle_index' => $vehicleIndex,
                    'vin' => $vehicle->getVin(),
                    'brand' => $vehicle->getBrand(),
                    'model' => $vehicle->getModel(),
                    'status' => $vehicle->getStatus()
                ]);

                $vehicles[] = [
                    'vin' => $vehicle->getVin(),
                    'brand' => $vehicle->getBrand(),
                    'model' => $vehicle->getModel(),
                    'version' => $vehicle->getVersion(),
                    'registrationNumber' => $vehicle->getRegistrationNumber(),
                    'registrationDate' => $vehicle->getRegistrationDate()?->format('Y-m-d'),
                    'color' => $vehicle->getColor(),
                    'energy' => $vehicle->getEnergy(),
                    'status' => $vehicle->getStatus(),
                    'featureCode' => $vehicle->getFeatureCode(),
                ];
                $vehicleIndex++;
            }

            $this->logger->info('Vehicle processing completed', [
                'total_vehicles_processed' => count($vehicles)
            ]);

            $result = [
                'id' => $user->getId(),
                'userId' => $user->getUserId(),
                'vehicles' => $vehicles,
                'userPsaId' => $user->getUserPsaId(),
            ];

            $this->logger->info('Returning user data', [
                'userId' => $userId,
                'result_keys' => array_keys($result),
                'vehicles_count' => count($result['vehicles']),
                'userPsaId_count' => $result['userPsaId'] ? count($result['userPsaId']) : 0
            ]);

            return $result;
        } catch (\Exception $e) {
            $this->logger->error('Error getting user by user ID', [
                'userId' => $userId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'file' => $e->getFile(),
                'line' => $e->getLine()
            ]);
            return [];
        }
    }

    /**
     * Updates the feature code and status for a specific vehicle of a user in the MongoDB collection.
     *
     * @param string $userId       The ID of the user.
     * @param string $vin          The Vehicle Identification Number (VIN) of the vehicle to update.
     * @param string $featureCode  The feature code to set for the vehicle.
     * @param string $featureStatus The status of the feature to set for the vehicle.
     */
    public function updateVehicleFeatureCode($userId, $vin, $featureCode, $featureStatus): bool
    {
        try {
            $user = $this->mongoDBService->findOneBy(UserData::class, ['userId' => $userId]);

            if (!$user) {
                $this->logger->warning('User not found for feature code update', [
                    'userId' => $userId,
                    'vin' => $vin,
                    'featureCode' => $featureCode
                ]);
                return false;
            }

            $vehicleFound = false;
            foreach ($user->getVehicles() as $vehicle) {
                if ($vehicle->getVin() === $vin) {
                    $vehicleFound = true;
                    // Note: The Vehicle document class doesn't have featureCode field
                    // This would need to be added to the Vehicle document class
                    // For now, we'll log that the vehicle was found
                    $this->logger->info('Vehicle found for feature code update', [
                        'userId' => $userId,
                        'vin' => $vin,
                        'featureCode' => $featureCode,
                        'featureStatus' => $featureStatus
                    ]);
                    break;
                }
            }

            if (!$vehicleFound) {
                $this->logger->warning('Vehicle not found for feature code update', [
                    'userId' => $userId,
                    'vin' => $vin,
                    'featureCode' => $featureCode
                ]);
                return false;
            }

            // Save the user document
            $this->mongoDBService->save($user);

            return true;
        } catch (\Exception $e) {
            $this->logger->error(
                sprintf('%s: Error updating vehicle featureCode', __METHOD__),
                [
                    'user_id' => $userId,
                    'vin' => $vin,
                    'featureCode' => $featureCode,
                    'featureStatus' => $featureStatus,
                    'exception_code' => $e->getCode(),
                    'exception_message' => $e->getMessage(),
                ]
            );
            return false;
        }
    }
}
